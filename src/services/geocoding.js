// Geocoding service using OpenStreetMap Nominatim API (free)

import axios from 'axios';
import { cacheService } from './cache.js';
import { monitoringService } from './monitoring.js';
import { performance } from 'perf_hooks';

export class GeocodingService {
  constructor() {
    this.nominatimUrl = 'https://nominatim.openstreetmap.org';
    this.userAgent = process.env.NOMINATIM_USER_AGENT || 'farm-scheduler-v1.0';
    // Respect rate limit: 1 request per second
    this.lastRequestTime = 0;
    this.minRequestInterval = 1000; // 1 second
  }

  async enforceRateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    
    if (timeSinceLastRequest < this.minRequestInterval) {
      const waitTime = this.minRequestInterval - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.lastRequestTime = Date.now();
  }

  validateCoordinates(latitude, longitude) {
    if (typeof latitude !== 'number' || typeof longitude !== 'number') {
      throw new Error('Latitude and longitude must be numbers');
    }

    if (latitude < -90 || latitude > 90) {
      throw new Error('Latitude must be between -90 and 90 degrees');
    }

    if (longitude < -180 || longitude > 180) {
      throw new Error('Longitude must be between -180 and 180 degrees');
    }
  }

  async reverseGeocode(latitude, longitude) {
    const startTime = performance.now();

    try {
      // Validate input coordinates
      this.validateCoordinates(latitude, longitude);

      // Check cache first
      const cachedData = cacheService.getCachedGeocodingData(latitude, longitude);
      if (cachedData) {
        monitoringService.trackServiceCall('geocoding', startTime, true);
        return cachedData;
      }

      await this.enforceRateLimit();

      const response = await axios.get(`${this.nominatimUrl}/reverse`, {
        params: {
          lat: latitude,
          lon: longitude,
          format: 'json',
          addressdetails: 1,
          extratags: 1,
          zoom: 18
        },
        headers: {
          'User-Agent': this.userAgent
        },
        timeout: 10000 // 10 second timeout
      });

      if (!response.data) {
        throw new Error('No data received from Nominatim');
      }

      const data = response.data;

      // Check if location was found
      if (data.error) {
        throw new Error(`Location not found: ${data.error}`);
      }

      const address = data.address || {};

      const result = {
        address: data.display_name,
        city: address.city || address.town || address.village || address.municipality || 'Unknown City',
        state: address.state || address.state_district || address.province || 'Unknown State',
        country: address.country || 'Unknown Country',
        countryCode: address.country_code?.toUpperCase() || 'XX',
        zipcode: address.postcode,
        district: address.district || address.county,
        latitude: parseFloat(data.lat),
        longitude: parseFloat(data.lon),
        source: 'OpenStreetMap'
      };

      // Cache the result
      cacheService.cacheGeocodingData(latitude, longitude, result);
      monitoringService.trackServiceCall('geocoding', startTime, true);

      return result;
    } catch (error) {
      console.error('OpenStreetMap geocoding error:', error.message);
      monitoringService.trackServiceCall('geocoding', startTime, false, error);

      // Return fallback location data for testing
      if (error.message.includes('timeout') || error.message.includes('network')) {
        console.log('Using fallback location data due to network issues');
        const fallbackData = {
          address: `Location at ${latitude}, ${longitude}`,
          city: 'Unknown City',
          state: 'Unknown State',
          country: 'Unknown Country',
          countryCode: 'XX',
          latitude: latitude,
          longitude: longitude,
          source: 'Fallback (Network Error)'
        };

        // Cache fallback data with shorter TTL
        cacheService.cacheGeocodingData(latitude, longitude, fallbackData);
        return fallbackData;
      }

      throw new Error(`Geocoding failed: ${error.message}`);
    }
  }
}