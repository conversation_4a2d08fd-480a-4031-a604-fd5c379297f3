// Production monitoring and performance tracking service

import moment from 'moment';
import { performance } from 'perf_hooks';

export class MonitoringService {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        byEndpoint: new Map(),
        responseTimeHistory: []
      },
      services: {
        weather: { calls: 0, failures: 0, avgResponseTime: 0 },
        geocoding: { calls: 0, failures: 0, avgResponseTime: 0 },
        llm: { calls: 0, failures: 0, avgResponseTime: 0 },
        youtube: { calls: 0, failures: 0, avgResponseTime: 0 },
        imageSearch: { calls: 0, failures: 0, avgResponseTime: 0 }
      },
      system: {
        startTime: Date.now(),
        uptime: 0,
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      },
      errors: [],
      performance: {
        slowRequests: [],
        averageResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0
      }
    };

    // Start periodic system monitoring
    this.startSystemMonitoring();
  }

  // Track API request metrics
  trackRequest(endpoint, method, startTime, success = true, error = null) {
    const responseTime = performance.now() - startTime;
    
    this.metrics.requests.total++;
    if (success) {
      this.metrics.requests.successful++;
    } else {
      this.metrics.requests.failed++;
      this.logError(endpoint, error);
    }

    // Track by endpoint
    const endpointKey = `${method} ${endpoint}`;
    if (!this.metrics.requests.byEndpoint.has(endpointKey)) {
      this.metrics.requests.byEndpoint.set(endpointKey, {
        count: 0,
        totalTime: 0,
        avgTime: 0,
        errors: 0
      });
    }

    const endpointMetrics = this.metrics.requests.byEndpoint.get(endpointKey);
    endpointMetrics.count++;
    endpointMetrics.totalTime += responseTime;
    endpointMetrics.avgTime = endpointMetrics.totalTime / endpointMetrics.count;
    if (!success) endpointMetrics.errors++;

    // Track response time history (keep last 1000 requests)
    this.metrics.requests.responseTimeHistory.push({
      timestamp: Date.now(),
      responseTime,
      endpoint: endpointKey,
      success
    });

    if (this.metrics.requests.responseTimeHistory.length > 1000) {
      this.metrics.requests.responseTimeHistory.shift();
    }

    // Track slow requests (>5 seconds)
    if (responseTime > 5000) {
      this.metrics.performance.slowRequests.push({
        timestamp: Date.now(),
        endpoint: endpointKey,
        responseTime,
        error: error?.message
      });

      // Keep only last 100 slow requests
      if (this.metrics.performance.slowRequests.length > 100) {
        this.metrics.performance.slowRequests.shift();
      }
    }

    this.updatePerformanceMetrics();
  }

  // Track service-specific metrics
  trackServiceCall(serviceName, startTime, success = true, error = null) {
    const responseTime = performance.now() - startTime;
    
    if (this.metrics.services[serviceName]) {
      const service = this.metrics.services[serviceName];
      service.calls++;
      if (!success) service.failures++;
      
      // Update average response time
      service.avgResponseTime = (service.avgResponseTime * (service.calls - 1) + responseTime) / service.calls;
    }

    if (!success && error) {
      this.logError(`Service: ${serviceName}`, error);
    }
  }

  // Log errors with context
  logError(context, error) {
    const errorEntry = {
      timestamp: Date.now(),
      context,
      message: error?.message || 'Unknown error',
      stack: error?.stack,
      type: error?.constructor?.name || 'Error'
    };

    this.metrics.errors.push(errorEntry);

    // Keep only last 500 errors
    if (this.metrics.errors.length > 500) {
      this.metrics.errors.shift();
    }

    // Log to console in development
    if (process.env.NODE_ENV !== 'production') {
      console.error(`[${context}] ${error?.message}`, error?.stack);
    }
  }

  // Update performance metrics
  updatePerformanceMetrics() {
    const responseTimes = this.metrics.requests.responseTimeHistory
      .map(r => r.responseTime)
      .sort((a, b) => a - b);

    if (responseTimes.length > 0) {
      this.metrics.performance.averageResponseTime = 
        responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;

      const p95Index = Math.floor(responseTimes.length * 0.95);
      const p99Index = Math.floor(responseTimes.length * 0.99);
      
      this.metrics.performance.p95ResponseTime = responseTimes[p95Index] || 0;
      this.metrics.performance.p99ResponseTime = responseTimes[p99Index] || 0;
    }
  }

  // Start periodic system monitoring
  startSystemMonitoring() {
    setInterval(() => {
      this.metrics.system.uptime = Date.now() - this.metrics.system.startTime;
      this.metrics.system.memoryUsage = process.memoryUsage();
      this.metrics.system.cpuUsage = process.cpuUsage();
    }, 30000); // Update every 30 seconds
  }

  // Get comprehensive system health report
  getHealthReport() {
    const now = Date.now();
    const last5Minutes = now - (5 * 60 * 1000);
    const recentErrors = this.metrics.errors.filter(e => e.timestamp > last5Minutes);
    const recentRequests = this.metrics.requests.responseTimeHistory.filter(r => r.timestamp > last5Minutes);

    return {
      status: this.getOverallStatus(),
      timestamp: now,
      uptime: {
        milliseconds: this.metrics.system.uptime,
        human: moment.duration(this.metrics.system.uptime).humanize()
      },
      requests: {
        total: this.metrics.requests.total,
        successful: this.metrics.requests.successful,
        failed: this.metrics.requests.failed,
        successRate: this.metrics.requests.total > 0 ? 
          (this.metrics.requests.successful / this.metrics.requests.total * 100).toFixed(2) + '%' : '0%',
        recentCount: recentRequests.length,
        recentErrorCount: recentErrors.length
      },
      performance: {
        averageResponseTime: Math.round(this.metrics.performance.averageResponseTime),
        p95ResponseTime: Math.round(this.metrics.performance.p95ResponseTime),
        p99ResponseTime: Math.round(this.metrics.performance.p99ResponseTime),
        slowRequestsCount: this.metrics.performance.slowRequests.length
      },
      services: Object.fromEntries(
        Object.entries(this.metrics.services).map(([name, metrics]) => [
          name,
          {
            calls: metrics.calls,
            failures: metrics.failures,
            successRate: metrics.calls > 0 ? 
              ((metrics.calls - metrics.failures) / metrics.calls * 100).toFixed(2) + '%' : '0%',
            avgResponseTime: Math.round(metrics.avgResponseTime)
          }
        ])
      ),
      system: {
        memory: {
          used: Math.round(this.metrics.system.memoryUsage.heapUsed / 1024 / 1024) + ' MB',
          total: Math.round(this.metrics.system.memoryUsage.heapTotal / 1024 / 1024) + ' MB',
          external: Math.round(this.metrics.system.memoryUsage.external / 1024 / 1024) + ' MB'
        },
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      },
      errors: {
        recentCount: recentErrors.length,
        totalCount: this.metrics.errors.length,
        recentErrors: recentErrors.slice(-10).map(e => ({
          timestamp: moment(e.timestamp).format('YYYY-MM-DD HH:mm:ss'),
          context: e.context,
          message: e.message,
          type: e.type
        }))
      }
    };
  }

  // Determine overall system status
  getOverallStatus() {
    const recentErrors = this.metrics.errors.filter(e => 
      e.timestamp > Date.now() - (5 * 60 * 1000)
    ).length;

    const successRate = this.metrics.requests.total > 0 ? 
      (this.metrics.requests.successful / this.metrics.requests.total) : 1;

    const avgResponseTime = this.metrics.performance.averageResponseTime;

    if (recentErrors > 10 || successRate < 0.9 || avgResponseTime > 10000) {
      return 'unhealthy';
    } else if (recentErrors > 5 || successRate < 0.95 || avgResponseTime > 5000) {
      return 'degraded';
    } else {
      return 'healthy';
    }
  }

  // Get detailed metrics for specific endpoint
  getEndpointMetrics(endpoint) {
    return Object.fromEntries(
      Array.from(this.metrics.requests.byEndpoint.entries())
        .filter(([key]) => !endpoint || key.includes(endpoint))
        .map(([key, metrics]) => [key, {
          ...metrics,
          errorRate: metrics.count > 0 ? (metrics.errors / metrics.count * 100).toFixed(2) + '%' : '0%'
        }])
    );
  }

  // Reset metrics (useful for testing)
  reset() {
    this.metrics.requests.total = 0;
    this.metrics.requests.successful = 0;
    this.metrics.requests.failed = 0;
    this.metrics.requests.byEndpoint.clear();
    this.metrics.requests.responseTimeHistory = [];
    this.metrics.errors = [];
    this.metrics.performance.slowRequests = [];
    
    Object.keys(this.metrics.services).forEach(service => {
      this.metrics.services[service] = { calls: 0, failures: 0, avgResponseTime: 0 };
    });
  }
}

// Singleton instance
export const monitoringService = new MonitoringService();
