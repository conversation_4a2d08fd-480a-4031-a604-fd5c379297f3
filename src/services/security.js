// Security hardening service for production deployment

import crypto from 'crypto';
import { monitoringService } from './monitoring.js';

export class SecurityService {
  constructor() {
    this.securityConfig = {
      // Input validation settings
      maxRequestSize: 10 * 1024 * 1024, // 10MB
      maxStringLength: 10000,
      maxArrayLength: 1000,
      
      // Rate limiting for security
      suspiciousActivityThreshold: 50, // requests per minute
      blockDuration: 15 * 60 * 1000, // 15 minutes
      
      // Content security
      allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
      allowedContentTypes: ['application/json', 'text/plain'],
      
      // API key validation
      apiKeyPattern: /^[a-zA-Z0-9_-]{20,}$/,
      
      // Suspicious patterns
      suspiciousPatterns: [
        /(<script|javascript:|data:)/i,
        /(union|select|insert|delete|drop|create|alter)\s+/i,
        /(\.\.|\/etc\/|\/proc\/|\/sys\/)/i,
        /(eval\(|exec\(|system\()/i
      ]
    };

    this.blockedIPs = new Map();
    this.suspiciousActivity = new Map();
  }

  // Validate and sanitize input data
  validateInput(data, schema = {}) {
    const errors = [];
    
    try {
      // Check data size
      const dataSize = JSON.stringify(data).length;
      if (dataSize > this.securityConfig.maxRequestSize) {
        errors.push(`Request too large: ${dataSize} bytes (max: ${this.securityConfig.maxRequestSize})`);
      }

      // Validate against schema
      const validatedData = this.validateAgainstSchema(data, schema, errors);
      
      // Check for suspicious patterns
      this.checkSuspiciousPatterns(data, errors);
      
      if (errors.length > 0) {
        throw new SecurityError('Input validation failed', errors);
      }

      return validatedData;
    } catch (error) {
      if (error instanceof SecurityError) {
        throw error;
      }
      throw new SecurityError('Input validation error', [error.message]);
    }
  }

  // Validate data against schema
  validateAgainstSchema(data, schema, errors) {
    const validated = {};
    
    for (const [key, rules] of Object.entries(schema)) {
      const value = data[key];
      
      // Check required fields
      if (rules.required && (value === undefined || value === null)) {
        errors.push(`Missing required field: ${key}`);
        continue;
      }
      
      if (value === undefined || value === null) {
        continue;
      }
      
      // Type validation
      if (rules.type && typeof value !== rules.type) {
        errors.push(`Invalid type for ${key}: expected ${rules.type}, got ${typeof value}`);
        continue;
      }
      
      // String validation
      if (rules.type === 'string') {
        if (rules.maxLength && value.length > rules.maxLength) {
          errors.push(`String too long for ${key}: ${value.length} (max: ${rules.maxLength})`);
          continue;
        }
        
        if (rules.pattern && !rules.pattern.test(value)) {
          errors.push(`Invalid format for ${key}`);
          continue;
        }
      }
      
      // Number validation
      if (rules.type === 'number') {
        if (rules.min !== undefined && value < rules.min) {
          errors.push(`Value too small for ${key}: ${value} (min: ${rules.min})`);
          continue;
        }
        
        if (rules.max !== undefined && value > rules.max) {
          errors.push(`Value too large for ${key}: ${value} (max: ${rules.max})`);
          continue;
        }
      }
      
      // Array validation
      if (Array.isArray(value)) {
        if (value.length > this.securityConfig.maxArrayLength) {
          errors.push(`Array too large for ${key}: ${value.length} (max: ${this.securityConfig.maxArrayLength})`);
          continue;
        }
      }
      
      validated[key] = value;
    }
    
    return validated;
  }

  // Check for suspicious patterns in data
  checkSuspiciousPatterns(data, errors) {
    const dataString = JSON.stringify(data).toLowerCase();
    
    for (const pattern of this.securityConfig.suspiciousPatterns) {
      if (pattern.test(dataString)) {
        errors.push('Suspicious content detected');
        break;
      }
    }
  }

  // Check if IP is blocked
  isIPBlocked(ip) {
    const blockInfo = this.blockedIPs.get(ip);
    
    if (!blockInfo) {
      return false;
    }
    
    if (Date.now() > blockInfo.expiresAt) {
      this.blockedIPs.delete(ip);
      return false;
    }
    
    return true;
  }

  // Block IP address
  blockIP(ip, reason = 'Suspicious activity', duration = null) {
    const blockDuration = duration || this.securityConfig.blockDuration;
    
    this.blockedIPs.set(ip, {
      reason,
      blockedAt: Date.now(),
      expiresAt: Date.now() + blockDuration
    });
    
    monitoringService.logError('Security', new Error(`IP blocked: ${ip} - ${reason}`));
    
    console.warn(`🚨 Security: Blocked IP ${ip} for ${reason}`);
  }

  // Track suspicious activity
  trackSuspiciousActivity(ip, activity) {
    const now = Date.now();
    const windowStart = now - 60000; // 1 minute window
    
    if (!this.suspiciousActivity.has(ip)) {
      this.suspiciousActivity.set(ip, []);
    }
    
    const activities = this.suspiciousActivity.get(ip);
    
    // Remove old activities
    const recentActivities = activities.filter(a => a.timestamp > windowStart);
    recentActivities.push({ activity, timestamp: now });
    
    this.suspiciousActivity.set(ip, recentActivities);
    
    // Check if threshold exceeded
    if (recentActivities.length > this.securityConfig.suspiciousActivityThreshold) {
      this.blockIP(ip, `Excessive requests: ${recentActivities.length}/min`);
      return true;
    }
    
    return false;
  }

  // Validate API key format
  validateAPIKey(apiKey) {
    if (!apiKey) {
      throw new SecurityError('API key required');
    }
    
    if (!this.securityConfig.apiKeyPattern.test(apiKey)) {
      throw new SecurityError('Invalid API key format');
    }
    
    return true;
  }

  // Generate secure random token
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  // Hash sensitive data
  hashData(data, salt = null) {
    const actualSalt = salt || crypto.randomBytes(16).toString('hex');
    const hash = crypto.pbkdf2Sync(data, actualSalt, 10000, 64, 'sha512').toString('hex');
    
    return {
      hash,
      salt: actualSalt
    };
  }

  // Verify hashed data
  verifyHash(data, hash, salt) {
    const computed = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512').toString('hex');
    return computed === hash;
  }

  // Sanitize output data (remove sensitive fields)
  sanitizeOutput(data, sensitiveFields = ['password', 'apiKey', 'secret', 'token']) {
    if (typeof data !== 'object' || data === null) {
      return data;
    }
    
    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeOutput(item, sensitiveFields));
    }
    
    const sanitized = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof value === 'object') {
        sanitized[key] = this.sanitizeOutput(value, sensitiveFields);
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }

  // Express middleware for security
  middleware(options = {}) {
    const {
      enableIPBlocking = true,
      enableInputValidation = true,
      enableSuspiciousTracking = true,
      schema = null
    } = options;

    return (req, res, next) => {
      const clientIP = req.ip || req.connection.remoteAddress;
      
      try {
        // Check if IP is blocked
        if (enableIPBlocking && this.isIPBlocked(clientIP)) {
          const blockInfo = this.blockedIPs.get(clientIP);
          return res.status(403).json({
            error: 'Access Denied',
            message: 'Your IP has been temporarily blocked',
            reason: blockInfo.reason,
            expiresAt: new Date(blockInfo.expiresAt).toISOString()
          });
        }
        
        // Track suspicious activity
        if (enableSuspiciousTracking) {
          const blocked = this.trackSuspiciousActivity(clientIP, req.path);
          if (blocked) {
            return res.status(429).json({
              error: 'Too Many Requests',
              message: 'Suspicious activity detected. Access temporarily blocked.'
            });
          }
        }
        
        // Validate input if schema provided
        if (enableInputValidation && schema && req.body) {
          try {
            req.body = this.validateInput(req.body, schema);
          } catch (error) {
            if (error instanceof SecurityError) {
              return res.status(400).json({
                error: 'Invalid Input',
                message: 'Input validation failed',
                details: error.details
              });
            }
            throw error;
          }
        }
        
        next();
      } catch (error) {
        monitoringService.logError('Security Middleware', error);
        res.status(500).json({
          error: 'Security Error',
          message: 'An error occurred during security validation'
        });
      }
    };
  }

  // Get security statistics
  getSecurityStats() {
    const now = Date.now();
    const activeBlocks = Array.from(this.blockedIPs.entries())
      .filter(([, info]) => now < info.expiresAt);
    
    const recentActivity = Array.from(this.suspiciousActivity.entries())
      .map(([ip, activities]) => ({
        ip,
        recentCount: activities.filter(a => now - a.timestamp < 300000).length // 5 minutes
      }))
      .filter(item => item.recentCount > 0);

    return {
      blockedIPs: activeBlocks.length,
      suspiciousIPs: recentActivity.length,
      totalBlocks: this.blockedIPs.size,
      activeBlocks: activeBlocks.map(([ip, info]) => ({
        ip,
        reason: info.reason,
        blockedAt: new Date(info.blockedAt).toISOString(),
        expiresAt: new Date(info.expiresAt).toISOString()
      })),
      recentActivity: recentActivity.slice(0, 10) // Top 10
    };
  }

  // Clear expired blocks and activities
  cleanup() {
    const now = Date.now();
    
    // Clean expired IP blocks
    for (const [ip, info] of this.blockedIPs.entries()) {
      if (now > info.expiresAt) {
        this.blockedIPs.delete(ip);
      }
    }
    
    // Clean old suspicious activities
    for (const [ip, activities] of this.suspiciousActivity.entries()) {
      const recent = activities.filter(a => now - a.timestamp < 3600000); // 1 hour
      if (recent.length === 0) {
        this.suspiciousActivity.delete(ip);
      } else {
        this.suspiciousActivity.set(ip, recent);
      }
    }
  }
}

// Custom security error class
export class SecurityError extends Error {
  constructor(message, details = []) {
    super(message);
    this.name = 'SecurityError';
    this.details = details;
  }
}

// Singleton instance
export const securityService = new SecurityService();

// Start periodic cleanup
setInterval(() => {
  securityService.cleanup();
}, 5 * 60 * 1000); // Every 5 minutes
