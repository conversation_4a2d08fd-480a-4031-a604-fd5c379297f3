// Load testing service for performance validation

import axios from 'axios';
import { performance } from 'perf_hooks';
import moment from 'moment';

export class LoadTesterService {
  constructor(baseUrl = 'http://localhost:3000') {
    this.baseUrl = baseUrl;
    this.results = {
      tests: [],
      summary: null,
      startTime: null,
      endTime: null
    };
  }

  // Run comprehensive load test suite
  async runLoadTests(options = {}) {
    const {
      concurrentUsers = 10,
      testDuration = 60, // seconds
      rampUpTime = 10, // seconds
      endpoints = this.getDefaultEndpoints(),
      verbose = false
    } = options;

    console.log(`🚀 Starting load test with ${concurrentUsers} concurrent users for ${testDuration}s`);
    
    this.results.startTime = Date.now();
    this.results.tests = [];

    // Test each endpoint
    for (const endpoint of endpoints) {
      console.log(`\n📊 Testing endpoint: ${endpoint.method} ${endpoint.path}`);
      
      const testResult = await this.testEndpoint(
        endpoint,
        concurrentUsers,
        testDuration,
        rampUpTime,
        verbose
      );
      
      this.results.tests.push(testResult);
      
      // Brief pause between endpoint tests
      await this.sleep(2000);
    }

    this.results.endTime = Date.now();
    this.results.summary = this.generateSummary();

    return this.results;
  }

  // Test a specific endpoint with load
  async testEndpoint(endpoint, concurrentUsers, duration, rampUpTime, verbose = false) {
    const testResults = {
      endpoint: `${endpoint.method} ${endpoint.path}`,
      concurrentUsers,
      duration,
      requests: [],
      errors: [],
      startTime: Date.now(),
      endTime: null
    };

    const workers = [];
    const requestsPerSecond = [];
    let totalRequests = 0;
    let totalErrors = 0;

    // Create worker promises
    for (let i = 0; i < concurrentUsers; i++) {
      const delay = (rampUpTime * 1000 * i) / concurrentUsers; // Stagger start times
      
      workers.push(
        this.createWorker(
          endpoint,
          duration * 1000,
          delay,
          (request) => {
            testResults.requests.push(request);
            totalRequests++;
            if (verbose && totalRequests % 100 === 0) {
              console.log(`  📈 Completed ${totalRequests} requests`);
            }
          },
          (error) => {
            testResults.errors.push(error);
            totalErrors++;
          }
        )
      );
    }

    // Track requests per second
    const rpsInterval = setInterval(() => {
      const currentRps = testResults.requests.filter(
        r => Date.now() - r.timestamp < 1000
      ).length;
      requestsPerSecond.push(currentRps);
    }, 1000);

    // Wait for all workers to complete
    await Promise.all(workers);
    clearInterval(rpsInterval);

    testResults.endTime = Date.now();
    testResults.stats = this.calculateStats(testResults, requestsPerSecond);

    console.log(`  ✅ Completed: ${totalRequests} requests, ${totalErrors} errors`);
    console.log(`  📊 Avg Response Time: ${testResults.stats.avgResponseTime}ms`);
    console.log(`  🚀 Peak RPS: ${testResults.stats.peakRps}`);

    return testResults;
  }

  // Create a worker that makes requests for the specified duration
  async createWorker(endpoint, duration, delay, onRequest, onError) {
    // Wait for ramp-up delay
    if (delay > 0) {
      await this.sleep(delay);
    }

    const endTime = Date.now() + duration;
    
    while (Date.now() < endTime) {
      const startTime = performance.now();
      
      try {
        const response = await this.makeRequest(endpoint);
        const responseTime = performance.now() - startTime;
        
        onRequest({
          timestamp: Date.now(),
          responseTime,
          statusCode: response.status,
          success: response.status < 400
        });
        
      } catch (error) {
        const responseTime = performance.now() - startTime;
        
        onError({
          timestamp: Date.now(),
          responseTime,
          error: error.message,
          statusCode: error.response?.status || 0
        });
      }

      // Small delay to prevent overwhelming the server
      await this.sleep(Math.random() * 100 + 50); // 50-150ms
    }
  }

  // Make HTTP request to endpoint
  async makeRequest(endpoint) {
    const config = {
      method: endpoint.method,
      url: `${this.baseUrl}${endpoint.path}`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (endpoint.data) {
      config.data = endpoint.data;
    }

    return await axios(config);
  }

  // Calculate statistics for test results
  calculateStats(testResults, requestsPerSecond) {
    const requests = testResults.requests;
    const errors = testResults.errors;
    const totalRequests = requests.length + errors.length;

    if (totalRequests === 0) {
      return {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        successRate: 0,
        avgResponseTime: 0,
        minResponseTime: 0,
        maxResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        avgRps: 0,
        peakRps: 0
      };
    }

    const responseTimes = [
      ...requests.map(r => r.responseTime),
      ...errors.map(e => e.responseTime)
    ].sort((a, b) => a - b);

    const successRate = (requests.length / totalRequests) * 100;
    const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    
    const p95Index = Math.floor(responseTimes.length * 0.95);
    const p99Index = Math.floor(responseTimes.length * 0.99);

    const testDurationSeconds = (testResults.endTime - testResults.startTime) / 1000;
    const avgRps = totalRequests / testDurationSeconds;

    return {
      totalRequests,
      successfulRequests: requests.length,
      failedRequests: errors.length,
      successRate: Math.round(successRate * 100) / 100,
      avgResponseTime: Math.round(avgResponseTime),
      minResponseTime: Math.round(Math.min(...responseTimes)),
      maxResponseTime: Math.round(Math.max(...responseTimes)),
      p95ResponseTime: Math.round(responseTimes[p95Index] || 0),
      p99ResponseTime: Math.round(responseTimes[p99Index] || 0),
      avgRps: Math.round(avgRps * 100) / 100,
      peakRps: Math.max(...requestsPerSecond, 0),
      duration: Math.round(testDurationSeconds)
    };
  }

  // Generate overall test summary
  generateSummary() {
    const allRequests = this.results.tests.reduce((sum, test) => sum + test.stats.totalRequests, 0);
    const allSuccessful = this.results.tests.reduce((sum, test) => sum + test.stats.successfulRequests, 0);
    const allFailed = this.results.tests.reduce((sum, test) => sum + test.stats.failedRequests, 0);
    
    const avgResponseTimes = this.results.tests.map(test => test.stats.avgResponseTime);
    const overallAvgResponseTime = avgResponseTimes.reduce((sum, time) => sum + time, 0) / avgResponseTimes.length;
    
    const totalDuration = (this.results.endTime - this.results.startTime) / 1000;
    const overallRps = allRequests / totalDuration;

    return {
      totalTests: this.results.tests.length,
      totalRequests: allRequests,
      successfulRequests: allSuccessful,
      failedRequests: allFailed,
      overallSuccessRate: Math.round((allSuccessful / allRequests) * 10000) / 100,
      overallAvgResponseTime: Math.round(overallAvgResponseTime),
      overallRps: Math.round(overallRps * 100) / 100,
      totalDuration: Math.round(totalDuration),
      timestamp: moment().format('YYYY-MM-DD HH:mm:ss')
    };
  }

  // Get default endpoints to test
  getDefaultEndpoints() {
    return [
      {
        method: 'GET',
        path: '/health'
      },
      {
        method: 'GET',
        path: '/api/system/status'
      },
      {
        method: 'GET',
        path: '/api/weather/supported-activities'
      },
      {
        method: 'GET',
        path: '/api/weather/monitoring-status?latitude=12.9716&longitude=77.5946'
      },
      {
        method: 'POST',
        path: '/api/register',
        data: {
          phoneNumber: '+**********',
          latitude: 12.9716,
          longitude: 77.5946,
          preferences: { llm_provider: 'openai' }
        }
      }
    ];
  }

  // Generate detailed report
  generateReport() {
    const report = {
      summary: this.results.summary,
      testDetails: this.results.tests.map(test => ({
        endpoint: test.endpoint,
        stats: test.stats,
        errorSample: test.errors.slice(0, 5) // First 5 errors
      })),
      recommendations: this.generateRecommendations()
    };

    return report;
  }

  // Generate performance recommendations
  generateRecommendations() {
    const recommendations = [];
    
    for (const test of this.results.tests) {
      const stats = test.stats;
      
      if (stats.successRate < 95) {
        recommendations.push({
          type: 'error_rate',
          endpoint: test.endpoint,
          message: `High error rate (${stats.successRate}%) - investigate error handling`,
          priority: 'high'
        });
      }
      
      if (stats.avgResponseTime > 2000) {
        recommendations.push({
          type: 'response_time',
          endpoint: test.endpoint,
          message: `Slow response time (${stats.avgResponseTime}ms) - consider optimization`,
          priority: 'medium'
        });
      }
      
      if (stats.p99ResponseTime > 10000) {
        recommendations.push({
          type: 'tail_latency',
          endpoint: test.endpoint,
          message: `High tail latency (P99: ${stats.p99ResponseTime}ms) - check for bottlenecks`,
          priority: 'medium'
        });
      }
    }

    if (this.results.summary.overallSuccessRate > 99) {
      recommendations.push({
        type: 'performance',
        message: 'Excellent performance! System is production-ready',
        priority: 'info'
      });
    }

    return recommendations;
  }

  // Utility function for delays
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Reset test results
  reset() {
    this.results = {
      tests: [],
      summary: null,
      startTime: null,
      endTime: null
    };
  }
}

// Export singleton instance
export const loadTesterService = new LoadTesterService();
