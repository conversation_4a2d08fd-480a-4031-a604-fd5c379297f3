// Intelligent caching system for performance optimization

import moment from 'moment';

export class CacheService {
  constructor() {
    this.cache = new Map();
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      evictions: 0,
      totalSize: 0
    };
    
    // Cache configuration
    this.config = {
      maxSize: 1000, // Maximum number of cache entries
      defaultTTL: 15 * 60 * 1000, // 15 minutes default TTL
      cleanupInterval: 5 * 60 * 1000, // Cleanup every 5 minutes
      maxMemoryUsage: 50 * 1024 * 1024 // 50MB max memory usage
    };

    // Different TTL for different data types
    this.ttlConfig = {
      weather: 10 * 60 * 1000,      // 10 minutes
      geocoding: 24 * 60 * 60 * 1000, // 24 hours
      llm_context: 60 * 60 * 1000,   // 1 hour
      youtube: 30 * 60 * 1000,       // 30 minutes
      images: 60 * 60 * 1000,        // 1 hour
      schedule: 5 * 60 * 1000        // 5 minutes
    };

    // Start periodic cleanup
    this.startCleanup();
  }

  // Generate cache key with namespace
  generateKey(namespace, ...parts) {
    return `${namespace}:${parts.join(':')}`;
  }

  // Set cache entry with TTL
  set(key, value, ttl = null) {
    const namespace = key.split(':')[0];
    const actualTTL = ttl || this.ttlConfig[namespace] || this.config.defaultTTL;
    
    const entry = {
      value,
      timestamp: Date.now(),
      ttl: actualTTL,
      expiresAt: Date.now() + actualTTL,
      size: this.calculateSize(value),
      hits: 0,
      lastAccessed: Date.now()
    };

    // Check if we need to evict entries
    this.evictIfNeeded(entry.size);

    this.cache.set(key, entry);
    this.stats.sets++;
    this.stats.totalSize += entry.size;

    return true;
  }

  // Get cache entry
  get(key) {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.delete(key);
      this.stats.misses++;
      return null;
    }

    // Update access statistics
    entry.hits++;
    entry.lastAccessed = Date.now();
    this.stats.hits++;

    return entry.value;
  }

  // Delete cache entry
  delete(key) {
    const entry = this.cache.get(key);
    if (entry) {
      this.stats.totalSize -= entry.size;
      this.cache.delete(key);
      return true;
    }
    return false;
  }

  // Check if key exists and is not expired
  has(key) {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (Date.now() > entry.expiresAt) {
      this.delete(key);
      return false;
    }
    
    return true;
  }

  // Cache weather data with location-based key
  cacheWeatherData(latitude, longitude, data, type = 'current') {
    const key = this.generateKey('weather', type, latitude.toFixed(4), longitude.toFixed(4));
    return this.set(key, data);
  }

  // Get cached weather data
  getCachedWeatherData(latitude, longitude, type = 'current') {
    const key = this.generateKey('weather', type, latitude.toFixed(4), longitude.toFixed(4));
    return this.get(key);
  }

  // Cache geocoding results
  cacheGeocodingData(latitude, longitude, data) {
    const key = this.generateKey('geocoding', latitude.toFixed(4), longitude.toFixed(4));
    return this.set(key, data);
  }

  // Get cached geocoding data
  getCachedGeocodingData(latitude, longitude) {
    const key = this.generateKey('geocoding', latitude.toFixed(4), longitude.toFixed(4));
    return this.get(key);
  }

  // Cache LLM responses
  cacheLLMResponse(prompt, provider, data) {
    const promptHash = this.hashString(prompt);
    const key = this.generateKey('llm_context', provider, promptHash);
    return this.set(key, data);
  }

  // Get cached LLM response
  getCachedLLMResponse(prompt, provider) {
    const promptHash = this.hashString(prompt);
    const key = this.generateKey('llm_context', provider, promptHash);
    return this.get(key);
  }

  // Cache YouTube search results
  cacheYouTubeResults(query, data) {
    const queryHash = this.hashString(query);
    const key = this.generateKey('youtube', queryHash);
    return this.set(key, data);
  }

  // Get cached YouTube results
  getCachedYouTubeResults(query) {
    const queryHash = this.hashString(query);
    const key = this.generateKey('youtube', queryHash);
    return this.get(key);
  }

  // Cache schedule data
  cacheSchedule(userProfile, cropContext, weekData, schedule) {
    const scheduleHash = this.hashString(JSON.stringify({
      location: userProfile.location,
      crop: cropContext.crop_info.name,
      week: weekData.weekNumber
    }));
    const key = this.generateKey('schedule', scheduleHash);
    return this.set(key, schedule);
  }

  // Get cached schedule
  getCachedSchedule(userProfile, cropContext, weekData) {
    const scheduleHash = this.hashString(JSON.stringify({
      location: userProfile.location,
      crop: cropContext.crop_info.name,
      week: weekData.weekNumber
    }));
    const key = this.generateKey('schedule', scheduleHash);
    return this.get(key);
  }

  // Calculate approximate size of data
  calculateSize(data) {
    try {
      return JSON.stringify(data).length * 2; // Rough estimate (UTF-16)
    } catch {
      return 1000; // Default size for non-serializable data
    }
  }

  // Simple string hashing function
  hashString(str) {
    let hash = 0;
    if (str.length === 0) return hash;
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  // Evict entries if needed (LRU strategy)
  evictIfNeeded(newEntrySize) {
    // Check size limit
    if (this.cache.size >= this.config.maxSize || 
        this.stats.totalSize + newEntrySize > this.config.maxMemoryUsage) {
      
      // Sort by last accessed time (LRU)
      const entries = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

      // Evict oldest entries
      const toEvict = Math.max(1, Math.floor(this.cache.size * 0.1)); // Evict 10%
      
      for (let i = 0; i < toEvict && entries.length > 0; i++) {
        const [key, entry] = entries[i];
        this.stats.totalSize -= entry.size;
        this.cache.delete(key);
        this.stats.evictions++;
      }
    }
  }

  // Start periodic cleanup of expired entries
  startCleanup() {
    setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  // Clean up expired entries
  cleanup() {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      this.delete(key);
    });

    if (expiredKeys.length > 0) {
      console.log(`Cache cleanup: removed ${expiredKeys.length} expired entries`);
    }
  }

  // Get cache statistics
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0 ? 
      (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2) : 0;

    return {
      ...this.stats,
      hitRate: hitRate + '%',
      size: this.cache.size,
      memoryUsage: Math.round(this.stats.totalSize / 1024 / 1024 * 100) / 100 + ' MB',
      averageEntrySize: this.cache.size > 0 ? 
        Math.round(this.stats.totalSize / this.cache.size) + ' bytes' : '0 bytes'
    };
  }

  // Get cache contents by namespace
  getByNamespace(namespace) {
    const results = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (key.startsWith(namespace + ':')) {
        results.push({
          key,
          size: entry.size,
          hits: entry.hits,
          expiresAt: moment(entry.expiresAt).format('YYYY-MM-DD HH:mm:ss'),
          lastAccessed: moment(entry.lastAccessed).format('YYYY-MM-DD HH:mm:ss')
        });
      }
    }
    
    return results;
  }

  // Clear all cache entries
  clear() {
    this.cache.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      evictions: 0,
      totalSize: 0
    };
  }

  // Clear cache by namespace
  clearNamespace(namespace) {
    const keysToDelete = [];
    
    for (const key of this.cache.keys()) {
      if (key.startsWith(namespace + ':')) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.delete(key));
    return keysToDelete.length;
  }
}

// Singleton instance
export const cacheService = new CacheService();
