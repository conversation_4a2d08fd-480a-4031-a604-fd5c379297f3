// Rate limiting service for API protection and fair usage

import moment from 'moment';

export class RateLimiterService {
  constructor() {
    this.limits = new Map(); // Store rate limit data per identifier
    this.config = {
      // Default rate limits (requests per time window)
      default: { requests: 100, window: 60 * 1000 }, // 100 requests per minute
      
      // Endpoint-specific limits
      endpoints: {
        '/api/register': { requests: 5, window: 60 * 1000 }, // 5 registrations per minute
        '/api/schedule': { requests: 20, window: 60 * 1000 }, // 20 schedules per minute
        '/api/weather': { requests: 30, window: 60 * 1000 }, // 30 weather requests per minute
        '/api/system/status': { requests: 60, window: 60 * 1000 } // 60 status checks per minute
      },
      
      // Service-specific limits (for internal API calls)
      services: {
        weather: { requests: 1000, window: 60 * 60 * 1000 }, // 1000 per hour
        geocoding: { requests: 500, window: 60 * 60 * 1000 }, // 500 per hour
        llm: { requests: 100, window: 60 * 60 * 1000 }, // 100 per hour
        youtube: { requests: 200, window: 60 * 60 * 1000 }, // 200 per hour
        imageSearch: { requests: 150, window: 60 * 60 * 1000 } // 150 per hour
      },
      
      // IP-based limits
      ip: { requests: 200, window: 60 * 1000 }, // 200 requests per minute per IP
      
      // User-based limits (if authenticated)
      user: { requests: 500, window: 60 * 1000 } // 500 requests per minute per user
    };

    // Cleanup expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  // Check if request is allowed
  isAllowed(identifier, type = 'default', endpoint = null) {
    const limit = this.getLimit(type, endpoint);
    const key = `${type}:${identifier}`;
    
    const now = Date.now();
    const windowStart = now - limit.window;

    // Get or create rate limit data
    if (!this.limits.has(key)) {
      this.limits.set(key, {
        requests: [],
        blocked: false,
        blockedUntil: 0,
        totalRequests: 0,
        totalBlocked: 0
      });
    }

    const data = this.limits.get(key);

    // Remove old requests outside the window
    data.requests = data.requests.filter(timestamp => timestamp > windowStart);

    // Check if currently blocked
    if (data.blocked && now < data.blockedUntil) {
      return {
        allowed: false,
        reason: 'rate_limited',
        retryAfter: Math.ceil((data.blockedUntil - now) / 1000),
        limit: limit.requests,
        remaining: 0,
        resetTime: data.blockedUntil
      };
    }

    // Reset block status if block period has expired
    if (data.blocked && now >= data.blockedUntil) {
      data.blocked = false;
      data.blockedUntil = 0;
    }

    // Check if limit exceeded
    if (data.requests.length >= limit.requests) {
      // Block for the remaining window time
      const oldestRequest = Math.min(...data.requests);
      const blockUntil = oldestRequest + limit.window;
      
      data.blocked = true;
      data.blockedUntil = blockUntil;
      data.totalBlocked++;

      return {
        allowed: false,
        reason: 'rate_limited',
        retryAfter: Math.ceil((blockUntil - now) / 1000),
        limit: limit.requests,
        remaining: 0,
        resetTime: blockUntil
      };
    }

    // Allow request and record it
    data.requests.push(now);
    data.totalRequests++;

    return {
      allowed: true,
      limit: limit.requests,
      remaining: limit.requests - data.requests.length,
      resetTime: now + limit.window
    };
  }

  // Record a request (for tracking purposes)
  recordRequest(identifier, type = 'default', endpoint = null) {
    return this.isAllowed(identifier, type, endpoint);
  }

  // Get rate limit configuration
  getLimit(type, endpoint = null) {
    if (endpoint && this.config.endpoints[endpoint]) {
      return this.config.endpoints[endpoint];
    }
    
    if (this.config[type]) {
      return this.config[type];
    }
    
    return this.config.default;
  }

  // Check service rate limit
  checkServiceLimit(serviceName, identifier = 'global') {
    return this.isAllowed(identifier, 'services', serviceName);
  }

  // Check IP rate limit
  checkIPLimit(ipAddress) {
    return this.isAllowed(ipAddress, 'ip');
  }

  // Check user rate limit
  checkUserLimit(userId) {
    return this.isAllowed(userId, 'user');
  }

  // Check endpoint-specific rate limit
  checkEndpointLimit(identifier, endpoint) {
    return this.isAllowed(identifier, 'endpoints', endpoint);
  }

  // Get rate limit status for identifier
  getStatus(identifier, type = 'default') {
    const key = `${type}:${identifier}`;
    const data = this.limits.get(key);
    const limit = this.getLimit(type);
    
    if (!data) {
      return {
        requests: 0,
        limit: limit.requests,
        remaining: limit.requests,
        blocked: false,
        totalRequests: 0,
        totalBlocked: 0
      };
    }

    const now = Date.now();
    const windowStart = now - limit.window;
    const currentRequests = data.requests.filter(timestamp => timestamp > windowStart);

    return {
      requests: currentRequests.length,
      limit: limit.requests,
      remaining: Math.max(0, limit.requests - currentRequests.length),
      blocked: data.blocked && now < data.blockedUntil,
      blockedUntil: data.blocked ? data.blockedUntil : null,
      totalRequests: data.totalRequests,
      totalBlocked: data.totalBlocked,
      window: limit.window
    };
  }

  // Get comprehensive rate limiting statistics
  getStats() {
    const stats = {
      totalIdentifiers: this.limits.size,
      activeBlocks: 0,
      totalRequests: 0,
      totalBlocked: 0,
      byType: {},
      topIdentifiers: []
    };

    const now = Date.now();
    const identifierStats = [];

    for (const [key, data] of this.limits.entries()) {
      const [type, identifier] = key.split(':', 2);
      
      stats.totalRequests += data.totalRequests;
      stats.totalBlocked += data.totalBlocked;
      
      if (data.blocked && now < data.blockedUntil) {
        stats.activeBlocks++;
      }

      // Group by type
      if (!stats.byType[type]) {
        stats.byType[type] = {
          identifiers: 0,
          requests: 0,
          blocked: 0,
          activeBlocks: 0
        };
      }
      
      stats.byType[type].identifiers++;
      stats.byType[type].requests += data.totalRequests;
      stats.byType[type].blocked += data.totalBlocked;
      
      if (data.blocked && now < data.blockedUntil) {
        stats.byType[type].activeBlocks++;
      }

      // Collect for top identifiers
      identifierStats.push({
        identifier: key,
        requests: data.totalRequests,
        blocked: data.totalBlocked,
        isBlocked: data.blocked && now < data.blockedUntil
      });
    }

    // Get top 10 most active identifiers
    stats.topIdentifiers = identifierStats
      .sort((a, b) => b.requests - a.requests)
      .slice(0, 10);

    return stats;
  }

  // Clean up expired entries
  cleanup() {
    const now = Date.now();
    const keysToDelete = [];

    for (const [key, data] of this.limits.entries()) {
      // Remove if no recent activity and not blocked
      const hasRecentActivity = data.requests.some(timestamp => 
        now - timestamp < 60 * 60 * 1000 // 1 hour
      );
      
      const isBlocked = data.blocked && now < data.blockedUntil;
      
      if (!hasRecentActivity && !isBlocked) {
        keysToDelete.push(key);
      } else {
        // Clean up old requests
        const limit = this.getLimit(key.split(':', 1)[0]);
        const windowStart = now - limit.window;
        data.requests = data.requests.filter(timestamp => timestamp > windowStart);
      }
    }

    keysToDelete.forEach(key => this.limits.delete(key));

    if (keysToDelete.length > 0) {
      console.log(`Rate limiter cleanup: removed ${keysToDelete.length} inactive entries`);
    }
  }

  // Reset rate limits for identifier
  reset(identifier, type = 'default') {
    const key = `${type}:${identifier}`;
    this.limits.delete(key);
    return true;
  }

  // Reset all rate limits
  resetAll() {
    this.limits.clear();
    return true;
  }

  // Update rate limit configuration
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }

  // Get current configuration
  getConfig() {
    return { ...this.config };
  }

  // Express middleware for rate limiting
  middleware(options = {}) {
    const {
      keyGenerator = (req) => req.ip,
      type = 'ip',
      endpoint = null,
      skipSuccessfulRequests = false,
      skipFailedRequests = false
    } = options;

    return (req, res, next) => {
      const identifier = keyGenerator(req);
      const endpointPath = endpoint || req.path;
      
      const result = this.isAllowed(identifier, type, endpointPath);
      
      // Set rate limit headers
      res.set({
        'X-RateLimit-Limit': result.limit,
        'X-RateLimit-Remaining': result.remaining,
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
      });

      if (!result.allowed) {
        res.set('Retry-After', result.retryAfter);
        return res.status(429).json({
          error: 'Too Many Requests',
          message: 'Rate limit exceeded',
          retryAfter: result.retryAfter,
          limit: result.limit
        });
      }

      next();
    };
  }
}

// Singleton instance
export const rateLimiterService = new RateLimiterService();
