// End-to-end integration tests for Farm Scheduler System

import { jest } from '@jest/globals';
import axios from 'axios';
import { performance } from 'perf_hooks';

const BASE_URL = 'http://localhost:3000';
const TEST_TIMEOUT = 60000; // 60 seconds

describe('End-to-End Integration Tests', () => {
  let testUserProfile = null;
  let testCropContext = null;

  beforeAll(async () => {
    // Wait for server to be ready
    await waitForServer();
  }, TEST_TIMEOUT);

  describe('System Health and Status', () => {
    test('should return healthy status', async () => {
      const response = await axios.get(`${BASE_URL}/health`);
      
      expect(response.status).toBe(200);
      expect(response.data.status).toBeDefined();
      expect(response.data.timestamp).toBeDefined();
    });

    test('should return system status with all services', async () => {
      const response = await axios.get(`${BASE_URL}/api/system/status`);
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.services).toBeDefined();
      expect(response.data.data.llm_providers).toBeDefined();
    });

    test('should return monitoring data', async () => {
      const response = await axios.get(`${BASE_URL}/api/system/monitoring`);
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.uptime).toBeDefined();
      expect(response.data.data.requests).toBeDefined();
      expect(response.data.data.performance).toBeDefined();
    });
  });

  describe('User Registration Flow', () => {
    test('should register a new user successfully', async () => {
      const registrationData = {
        phoneNumber: '+**********',
        latitude: 12.9716,
        longitude: 77.5946,
        preferences: {
          llm_provider: 'openai'
        }
      };

      const response = await axios.post(`${BASE_URL}/api/register`, registrationData);
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.phoneNumber).toBe(registrationData.phoneNumber);
      expect(response.data.data.location).toBeDefined();
      expect(response.data.data.currentWeather).toBeDefined();
      expect(response.data.data.locationContext).toBeDefined();

      // Store for later tests
      testUserProfile = response.data.data;
    }, TEST_TIMEOUT);

    test('should validate required registration fields', async () => {
      const invalidData = {
        phoneNumber: '+**********'
        // Missing latitude and longitude
      };

      try {
        await axios.post(`${BASE_URL}/api/register`, invalidData);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.error).toContain('required');
      }
    });
  });

  describe('Crop Context Generation', () => {
    test('should generate crop context successfully', async () => {
      if (!testUserProfile) {
        throw new Error('User profile not available from previous test');
      }

      const cropData = {
        userProfile: testUserProfile,
        cropType: 'tomato',
        plantingDate: '2024-01-15'
      };

      const response = await axios.post(`${BASE_URL}/api/crop/context`, cropData);
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.crop_info).toBeDefined();
      expect(response.data.data.crop_info.name).toBeDefined();
      expect(response.data.data.growth_stages).toBeDefined();

      // Store for later tests
      testCropContext = response.data.data;
    }, TEST_TIMEOUT);
  });

  describe('Schedule Generation', () => {
    test('should generate weekly schedule successfully', async () => {
      if (!testUserProfile || !testCropContext) {
        throw new Error('Prerequisites not available from previous tests');
      }

      const scheduleData = {
        userProfile: testUserProfile,
        cropContext: testCropContext
      };

      const response = await axios.post(`${BASE_URL}/api/schedule/week`, scheduleData);
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.week_info).toBeDefined();
      expect(response.data.data.activity).toBeDefined();
      expect(response.data.data.weather_analysis).toBeDefined();
      expect(response.data.data.learning_resources).toBeDefined();
    }, TEST_TIMEOUT);

    test('should generate multi-week schedule successfully', async () => {
      if (!testUserProfile || !testCropContext) {
        throw new Error('Prerequisites not available from previous tests');
      }

      const scheduleData = {
        userProfile: testUserProfile,
        cropContext: testCropContext,
        numberOfWeeks: 2
      };

      const response = await axios.post(`${BASE_URL}/api/schedule/multi-week`, scheduleData);
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.schedules).toBeDefined();
      expect(response.data.data.schedules.length).toBe(2);
      expect(response.data.data.summary).toBeDefined();
    }, TEST_TIMEOUT);
  });

  describe('Weather Integration', () => {
    test('should get weather monitoring status', async () => {
      const response = await axios.get(
        `${BASE_URL}/api/weather/monitoring-status?latitude=12.9716&longitude=77.5946`
      );
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.location).toBeDefined();
      expect(response.data.data.monitoring_active).toBeDefined();
    });

    test('should get supported activities', async () => {
      const response = await axios.get(`${BASE_URL}/api/weather/supported-activities`);
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.activities).toBeDefined();
      expect(Array.isArray(response.data.data.activities)).toBe(true);
    });

    test('should check weather impact on schedule', async () => {
      if (!testUserProfile) {
        throw new Error('User profile not available from previous tests');
      }

      const impactData = {
        userProfile: testUserProfile,
        scheduleData: {
          activity: { title: 'Watering', type: 'irrigation' },
          date: '2024-01-20'
        },
        activityType: 'irrigation'
      };

      const response = await axios.post(`${BASE_URL}/api/schedule/weather-impact`, impactData);
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.impact_level).toBeDefined();
      expect(response.data.data.recommendations).toBeDefined();
    }, TEST_TIMEOUT);
  });

  describe('Learning Resources', () => {
    test('should get video recommendations', async () => {
      if (!testCropContext || !testUserProfile) {
        throw new Error('Prerequisites not available from previous tests');
      }

      const videoData = {
        cropContext: testCropContext,
        weeklyActivity: { title: 'Watering', type: 'irrigation' },
        locationContext: testUserProfile.locationContext
      };

      const response = await axios.post(`${BASE_URL}/api/learning/videos`, videoData);
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.recommendations).toBeDefined();
      expect(response.data.data.web_search_suggestions).toBeDefined();
    }, TEST_TIMEOUT);

    test('should get pest and disease alerts', async () => {
      if (!testCropContext || !testUserProfile) {
        throw new Error('Prerequisites not available from previous tests');
      }

      const pestData = {
        cropContext: testCropContext,
        locationContext: testUserProfile.locationContext,
        currentWeather: testUserProfile.currentWeather
      };

      const response = await axios.post(`${BASE_URL}/api/pest-disease/alerts`, pestData);
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.alerts).toBeDefined();
    }, TEST_TIMEOUT);
  });

  describe('Performance and Monitoring', () => {
    test('should handle concurrent requests efficiently', async () => {
      const startTime = performance.now();
      const concurrentRequests = 10;
      
      const promises = Array(concurrentRequests).fill().map(() =>
        axios.get(`${BASE_URL}/api/system/status`)
      );

      const responses = await Promise.all(promises);
      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.data.success).toBe(true);
      });

      // Should complete within reasonable time (less than 10 seconds)
      expect(totalTime).toBeLessThan(10000);
      
      console.log(`✅ Concurrent test: ${concurrentRequests} requests in ${Math.round(totalTime)}ms`);
    });

    test('should return cache statistics', async () => {
      const response = await axios.get(`${BASE_URL}/api/system/cache`);
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.stats).toBeDefined();
      expect(response.data.data.stats.hitRate).toBeDefined();
    });

    test('should return rate limiting statistics', async () => {
      const response = await axios.get(`${BASE_URL}/api/system/rate-limits`);
      
      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.totalIdentifiers).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid endpoints gracefully', async () => {
      try {
        await axios.get(`${BASE_URL}/api/nonexistent`);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(404);
      }
    });

    test('should validate input data', async () => {
      const invalidData = {
        phoneNumber: 'invalid',
        latitude: 'not-a-number',
        longitude: 'not-a-number'
      };

      try {
        await axios.post(`${BASE_URL}/api/register`, invalidData);
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(400);
      }
    });

    test('should handle malformed JSON', async () => {
      try {
        await axios.post(`${BASE_URL}/api/register`, 'invalid-json', {
          headers: { 'Content-Type': 'application/json' }
        });
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(400);
      }
    });
  });
});

// Helper function to wait for server to be ready
async function waitForServer(maxAttempts = 30, delay = 1000) {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      await axios.get(`${BASE_URL}/health`);
      console.log('✅ Server is ready for testing');
      return;
    } catch (error) {
      if (i === maxAttempts - 1) {
        throw new Error('Server not ready after maximum attempts');
      }
      console.log(`⏳ Waiting for server... (attempt ${i + 1}/${maxAttempts})`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}
