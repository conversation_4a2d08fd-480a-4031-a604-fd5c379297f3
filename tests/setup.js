// Test setup file for Farm Scheduler System

import dotenv from 'dotenv';
import { jest } from '@jest/globals';

// Load environment variables for testing
dotenv.config();

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.PORT = '3001'; // Use different port for testing
process.env.DATABASE_URL = 'file:./test.db';

// Mock console methods to reduce noise during testing
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// Only show console output if VERBOSE_TESTS is set
if (!process.env.VERBOSE_TESTS) {
  console.log = jest.fn();
  console.warn = jest.fn();
  // Keep error logging for debugging
  console.error = (...args) => {
    if (args[0] && args[0].includes && args[0].includes('Test')) {
      originalConsoleError(...args);
    }
  };
}

// Global test timeout
jest.setTimeout(30000);

// Global setup
beforeAll(async () => {
  // Any global setup needed before all tests
});

// Global teardown
afterAll(async () => {
  // Restore console methods
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Setup before each test
beforeEach(() => {
  // Clear any mocks or reset state before each test
});

// Cleanup after each test
afterEach(() => {
  // Clean up after each test
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Mock external APIs for testing
export const mockAPIs = {
  openai: {
    chat: {
      completions: {
        create: jest.fn().mockResolvedValue({
          choices: [{
            message: {
              content: JSON.stringify({
                test: 'response'
              })
            }
          }]
        })
      }
    }
  },
  
  weather: {
    getCurrentWeather: jest.fn().mockResolvedValue({
      conditions: 'clear',
      temperature: { value: 25, unit: 'C' },
      humidity: 60,
      wind_speed: 3.5
    })
  },
  
  geocoding: {
    reverseGeocode: jest.fn().mockResolvedValue({
      city: 'Test City',
      state: 'Test State',
      country: 'Test Country',
      address: 'Test Address'
    })
  }
};

// Helper functions for tests
export const testHelpers = {
  // Wait for a specified amount of time
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Generate test user data
  generateTestUser: () => ({
    phoneNumber: `+${Math.floor(Math.random() * 9000000000) + 1000000000}`,
    latitude: 12.9716 + (Math.random() - 0.5) * 0.1,
    longitude: 77.5946 + (Math.random() - 0.5) * 0.1
  }),
  
  // Generate test crop data
  generateTestCrop: () => ({
    cropType: ['Wheat', 'Rice', 'Corn', 'Tomato'][Math.floor(Math.random() * 4)],
    plantingDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  }),
  
  // Validate API response structure
  validateApiResponse: (response, expectedFields = []) => {
    expect(response).toBeDefined();
    expect(response.status).toBe(200);
    expect(response.data).toBeDefined();
    expect(response.data.success).toBe(true);
    expect(response.data.data).toBeDefined();
    
    expectedFields.forEach(field => {
      expect(response.data.data[field]).toBeDefined();
    });
  },
  
  // Validate error response structure
  validateErrorResponse: (error, expectedStatus = 400) => {
    expect(error.response).toBeDefined();
    expect(error.response.status).toBe(expectedStatus);
    expect(error.response.data).toBeDefined();
    expect(error.response.data.error).toBeDefined();
  }
};

// Export for use in tests
export { mockAPIs as mocks, testHelpers as helpers };
