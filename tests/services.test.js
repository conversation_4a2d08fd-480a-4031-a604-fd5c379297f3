// Unit tests for Farm Scheduler Services

import { jest } from '@jest/globals';
import { GeocodingService } from '../src/services/geocoding.js';
import { WeatherService } from '../src/services/weather.js';
import { FarmSchedulerService } from '../src/services/scheduler.js';

// Mock environment variables
process.env.OPENWEATHER_API_KEY = 'test_key';
process.env.NOMINATIM_USER_AGENT = 'test_agent';

describe('Service Unit Tests', () => {
  describe('GeocodingService', () => {
    let geocodingService;

    beforeEach(() => {
      geocodingService = new GeocodingService();
    });

    test('should create instance with correct user agent', () => {
      expect(geocodingService.userAgent).toBe('test_agent');
    });

    test('should handle reverse geocoding with valid coordinates', async () => {
      // Mock the reverseGeocode method directly
      const mockResult = {
        city: 'Test City',
        state: 'Test State',
        country: 'Test Country',
        address: 'Test Location, Test City, Test State, Test Country'
      };

      // Mock the method directly
      geocodingService.reverseGeocode = jest.fn().mockResolvedValue(mockResult);

      const result = await geocodingService.reverseGeocode(12.9716, 77.5946);

      expect(result).toBeDefined();
      expect(result.city).toBe('Test City');
      expect(result.state).toBe('Test State');
      expect(result.country).toBe('Test Country');
    });

    test('should handle geocoding errors gracefully', async () => {
      // Mock axios to throw an error
      const originalAxios = global.axios;
      global.axios = {
        get: jest.fn().mockRejectedValue(new Error('Network error'))
      };

      try {
        await expect(geocodingService.reverseGeocode(999, 999)).rejects.toThrow();
      } finally {
        global.axios = originalAxios;
      }
    });
  });

  describe('WeatherService', () => {
    let weatherService;

    beforeEach(() => {
      weatherService = new WeatherService();
    });

    test('should create instance with API key', () => {
      expect(weatherService.apiKey).toBe('test_key');
    });

    test('should format weather data correctly', () => {
      const mockWeatherData = {
        weather: [{ main: 'Clear', description: 'clear sky' }],
        main: { temp: 25.5, humidity: 60, pressure: 1013, feels_like: 26.2 },
        wind: { speed: 3.5, deg: 180 },
        clouds: { all: 10 },
        sys: { sunrise: 1640995200, sunset: 1641038400 }
      };

      const formatted = weatherService.formatWeatherData(mockWeatherData);

      expect(formatted.conditions).toBe('clear sky');
      expect(formatted.temperature.value).toBe(26); // rounded
      expect(formatted.temperature.unit).toBe('C');
      expect(formatted.humidity).toBe(60);
      expect(formatted.wind_speed).toBe(3.5);
      expect(formatted.cloudiness).toBe(10);
      expect(formatted.pressure).toBe(1013);
    });

    test('should determine weather priority correctly', () => {
      const goodWeather = {
        conditions: 'clear sky',
        temperature: { value: 25 }
      };
      
      const badWeather = {
        conditions: 'heavy rain',
        temperature: { value: 25 }
      };

      expect(weatherService.getWeatherPriority(goodWeather)).toBe('optimal');
      expect(weatherService.getWeatherPriority(badWeather)).toBe('avoid');
    });

    test('should generate appropriate weather notes', () => {
      const rainyWeather = {
        conditions: 'rain',
        temperature: { value: 20 }
      };

      const note = weatherService.getWeatherNote(rainyWeather, 'Planting');
      expect(note).toContain('rain');
      expect(note.length).toBeGreaterThan(0);
    });
  });

  describe('FarmSchedulerService', () => {
    let schedulerService;

    beforeEach(() => {
      schedulerService = new FarmSchedulerService();
    });

    test('should create instance with all required services', () => {
      expect(schedulerService.geocodingService).toBeDefined();
      expect(schedulerService.weatherService).toBeDefined();
      expect(schedulerService.openAIService).toBeDefined();
      expect(schedulerService.youtubeService).toBeDefined();
      expect(schedulerService.imageSearchService).toBeDefined();
    });

    test('should calculate week data correctly', () => {
      const mockCropContext = {
        crop_info: {
          planting_date: '2025-05-01'
        }
      };

      const targetDate = new Date('2025-05-15'); // 2 weeks after planting
      const weekData = schedulerService.calculateWeekData(mockCropContext, targetDate);

      expect(weekData.weekNumber).toBe(3); // Week 3 since planting
      expect(weekData.dateRange).toBeDefined();
      expect(weekData.year).toBe(2025);
    });

    test('should handle user registration flow', async () => {
      // Mock all the service calls
      schedulerService.geocodingService.reverseGeocode = jest.fn().mockResolvedValue({
        city: 'Test City',
        state: 'Test State',
        country: 'Test Country',
        address: 'Test Address'
      });

      schedulerService.weatherService.getCurrentWeather = jest.fn().mockResolvedValue({
        conditions: 'clear',
        temperature: { value: 25, unit: 'C' }
      });

      schedulerService.openAIService.getLocationContext = jest.fn().mockResolvedValue({
        climate_zone: { zone_name: 'Tropical' },
        current_season: { name: 'Summer' },
        location: { city: 'Test City' }
      });

      const result = await schedulerService.processUserRegistration(
        '+1234567890',
        12.9716,
        77.5946
      );

      expect(result).toBeDefined();
      expect(result.phoneNumber).toBe('+1234567890');
      expect(result.location).toBeDefined();
      expect(result.currentWeather).toBeDefined();
      expect(result.locationContext).toBeDefined();
    });

    test('should handle crop context generation', async () => {
      const mockUserProfile = {
        locationContext: {
          location: { city: 'Test City' },
          climate_zone: { zone_name: 'Tropical' },
          current_season: { name: 'Summer' }
        }
      };

      schedulerService.openAIService.getCropContext = jest.fn().mockResolvedValue({
        crop_info: {
          name: 'Wheat',
          current_stage: 'Vegetative',
          planting_date: '2025-05-01'
        },
        stage_details: {
          week_in_stage: 2
        }
      });

      const result = await schedulerService.getCropContext(
        mockUserProfile,
        'Wheat',
        '2025-05-01'
      );

      expect(result).toBeDefined();
      expect(result.crop_info.name).toBe('Wheat');
      expect(result.crop_info.current_stage).toBe('Vegetative');
    });
  });

  describe('Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      const geocodingService = new GeocodingService();
      
      // Mock network failure
      const originalAxios = global.axios;
      global.axios = {
        get: jest.fn().mockRejectedValue(new Error('Network timeout'))
      };

      try {
        await expect(geocodingService.reverseGeocode(0, 0)).rejects.toThrow();
      } finally {
        global.axios = originalAxios;
      }
    });

    test('should handle invalid API responses', async () => {
      const weatherService = new WeatherService();
      
      // Test with invalid weather data
      const invalidData = null;
      
      expect(() => {
        weatherService.formatWeatherData(invalidData);
      }).toThrow();
    });

    test('should handle missing environment variables', () => {
      // Temporarily remove env var
      const originalKey = process.env.OPENWEATHER_API_KEY;
      delete process.env.OPENWEATHER_API_KEY;

      const weatherService = new WeatherService();
      expect(weatherService.apiKey).toBeUndefined();

      // Restore env var
      process.env.OPENWEATHER_API_KEY = originalKey;
    });
  });

  describe('Data Validation', () => {
    test('should validate coordinate ranges', () => {
      const geocodingService = new GeocodingService();
      
      // Test invalid coordinates
      expect(() => {
        geocodingService.validateCoordinates(999, 999);
      }).toThrow();
      
      expect(() => {
        geocodingService.validateCoordinates(-999, -999);
      }).toThrow();
    });

    test('should validate weather data structure', () => {
      const weatherService = new WeatherService();

      const validData = {
        weather: [{ main: 'Clear', description: 'clear sky' }],
        main: { temp: 25, humidity: 60, pressure: 1013 },
        wind: { speed: 3.5, deg: 180 },
        clouds: { all: 10 },
        sys: { sunrise: 1640995200, sunset: 1641038400 }
      };

      const invalidData = {
        weather: [],
        main: {}
      };

      expect(() => weatherService.formatWeatherData(validData)).not.toThrow();
      expect(() => weatherService.formatWeatherData(invalidData)).toThrow();
    });
  });
});
